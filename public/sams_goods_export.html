<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>山姆商品数据导出</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-left: 4px solid #4facfe;
            padding-left: 15px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .stats-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>山姆商品数据导出</h1>
            <p>Sam's Club Goods Data Export Center</p>
        </div>

        <div class="content">
            <!-- 统计信息区域 -->
            <div class="stats-section">
                <h3>📊 数据统计</h3>
                <div class="stats-grid" id="statsGrid">
                    <div class="stat-item">
                        <div class="stat-value" id="independentGoodsCount">-</div>
                        <div class="stat-label">独立商品总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalStores">-</div>
                        <div class="stat-label">门店数量</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalGoodsRecords">-</div>
                        <div class="stat-label">门店商品总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="hasStockCount">-</div>
                        <div class="stat-label">有库存商品</div>
                    </div>
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="form-section">
                <h3>🔍 筛选条件</h3>
                <form id="exportForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="storeId">门店选择</label>
                            <select id="storeId" name="store_id">
                                <option value="0">全部门店</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="categoryId">分类选择</label>
                            <select id="categoryId" name="category_id">
                                <option value="0">全部分类</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="hasStock">库存状态</label>
                            <select id="hasStock" name="has_stock">
                                <option value="-1">全部</option>
                                <option value="1">有库存</option>
                                <option value="0">无库存</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="dateFrom">更新时间起始</label>
                            <input type="date" id="dateFrom" name="date_from">
                        </div>
                        <div class="form-group">
                            <label for="dateTo">更新时间结束</label>
                            <input type="date" id="dateTo" name="date_to">
                        </div>
                        <div class="form-group">
                            <label for="limit">导出数量限制</label>
                            <input type="number" id="limit" name="limit" placeholder="0为不限制" min="0">
                        </div>
                    </div>
                </form>
            </div>


            <!-- 导出按钮 -->

            <div class="form-section">
                <h3>📦 独立商品导出</h3>
                <p style="color: #666; margin-bottom: 20px;">导出商品基础信息，支持门店和分类筛选。如选择门店，则只导出该门店有售的商品</p>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="exportIndependentGoods('csv')">
                        📄 导出独立商品CSV
                    </button>
                    <button class="btn btn-success" onclick="exportIndependentGoods('json')">
                        📋 导出独立商品JSON
                    </button>
                    <button class="btn btn-info" onclick="exportIndependentGoods('excel')">
                        📊 导出独立商品Excel
                    </button>
                </div>
            </div>

            <div class="form-section">
                <h3>🏪 门店商品导出</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="exportData('csv')">
                        📄 导出CSV格式
                    </button>
                    <button class="btn btn-success" onclick="exportData('json')">
                        📋 导出JSON格式
                    </button>
                    <button class="btn btn-info" onclick="exportData('excel')">
                        📊 导出Excel格式
                    </button>
                </div>
            </div>


            <!-- 提示信息 -->
            <div id="alertSuccess" class="alert alert-success"></div>
            <div id="alertError" class="alert alert-error"></div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3>正在导出数据...</h3>
            <p>请稍候，正在处理您的请求</p>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStoreList();
            loadCategoryList();
            loadStatistics();
        });

        // 加载门店列表
        async function loadStoreList() {
            try {
                const response = await fetch('/index.php/api/Sams.SamsGoodsExport/getStoreList');
                const result = await response.json();
                
                if (result.code === 200) {
                    const storeSelect = document.getElementById('storeId');
                    result.data.forEach(store => {
                        const option = document.createElement('option');
                        option.value = store.storeId;
                        option.textContent = `${store.name} (${store.city})`;
                        storeSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载门店列表失败:', error);
            }
        }

        // 加载分类列表
        async function loadCategoryList() {
            try {
                const response = await fetch('/index.php/api/Sams.SamsGoodsExport/getCategoryList');
                const result = await response.json();
                
                if (result.code === 200) {
                    const categorySelect = document.getElementById('categoryId');
                    result.data.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.groupingId;
                        option.textContent = `${'　'.repeat(category.level - 1)}${category.name}`;
                        categorySelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载分类列表失败:', error);
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/index.php/api/Sams.SamsGoodsExport/getExportStatistics');
                const result = await response.json();
                
                if (result.code === 200) {
                    const stats = result.data;
                    document.getElementById('independentGoodsCount').textContent = stats.independent_goods_count.toLocaleString();
                    document.getElementById('totalGoodsRecords').textContent = stats.total_goods_records.toLocaleString();
                    document.getElementById('totalStores').textContent = stats.total_stores;
                    document.getElementById('hasStockCount').textContent = stats.has_stock_count.toLocaleString();
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 导出数据
        async function exportData(format) {
            const form = document.getElementById('exportForm');
            const formData = new FormData(form);
            formData.append('format', format);

            // 显示加载遮罩
            showLoading(true);

            try {
                const response = await fetch('/index.php/api/Sams.SamsGoodsExport/exportAllGoods', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    
                    // 根据格式设置文件名
                    const timestamp = new Date().toISOString().slice(0,19).replace(/:/g, '');
                    let filename = `山姆商品数据导出_${timestamp}`;
                    switch(format) {
                        case 'json':
                            filename += '.json';
                            break;
                        case 'excel':
                            filename += '.xls';
                            break;
                        default:
                            filename += '.csv';
                    }
                    
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showAlert('success', '导出成功！文件已开始下载。');
                } else {
                    const result = await response.json();
                    showAlert('error', result.msg || '导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                showAlert('error', '导出失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 显示/隐藏加载遮罩
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }

        // 导出独立商品数据
        async function exportIndependentGoods(format) {
            const form = document.getElementById('exportForm');
            const formData = new FormData();

            // 传递筛选条件（包括门店筛选）
            formData.append('format', format);
            formData.append('store_id', form.store_id.value);
            formData.append('category_id', form.category_id.value);
            formData.append('status', 1); // 只导出正常状态的商品
            formData.append('limit', form.limit.value);

            // 显示加载遮罩
            showLoading(true);

            try {
                const response = await fetch('/index.php/api/Sams.SamsGoodsExport/exportIndependentGoods', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;

                    // 根据格式设置文件名
                    const timestamp = new Date().toISOString().slice(0,19).replace(/:/g, '');
                    let filename = `山姆独立商品数据导出_${timestamp}`;
                    switch(format) {
                        case 'json':
                            filename += '.json';
                            break;
                        case 'excel':
                            filename += '.xls';
                            break;
                        default:
                            filename += '.csv';
                    }

                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showAlert('success', '独立商品数据导出成功！文件已开始下载。');
                } else {
                    const result = await response.json();
                    showAlert('error', result.msg || '导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                showAlert('error', '导出失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 显示提示信息
        function showAlert(type, message) {
            const alertElement = document.getElementById(type === 'success' ? 'alertSuccess' : 'alertError');
            alertElement.textContent = message;
            alertElement.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
