<?php

namespace app\api\controller\Sams;

use app\common\model\SamClub;
use app\common\model\sam\Categories;
use app\common\model\sam\Goods;
use app\common\model\sam\store\Goods as StoreGoods;
use app\common\model\sam\Store;
use think\facade\Log;
use modules\plantask\library\Task;
use think\facade\Cache;
use think\Request;
use think\Response;
use OSS\OssClient;
use OSS\Core\OssException;
use OSS\Credentials\StaticCredentialsProvider;

/**
 * 山姆会员店商品数据采集
 */
class SamsGoods extends SamsBase
{
    public function getStoreList()
    {
        // 默认获取需要更新的门店（更新时间超过更新间隔的门店）
        $currentTime = time();
        $storeQuery = Store::where('type', 4)
            ->where('status', 1)
            ->where('store_goods_count','>',200)
            ->whereRaw('(update_time + update_interval) <= ?', [$currentTime])
            ->order('update_time', 'asc')
            ->limit(100);
        $stores = $storeQuery->select();
        foreach ($stores as $store) {
            echo $store->storeId . '<br>';
            echo $store->name . '<br>';
            echo date('Y-m-d H:i:s', $store->update_time) . '<br>';
            echo $store->update_interval . '<br>';
            echo '<br>';
        }
    }

    public function TaskStart()
    {
        $tenMinutesAgo = time() - 3600;
        $runningTasks = \app\admin\model\Plantask::where('status',  1)
            ->where('add_time', '<=', $tenMinutesAgo) // 最近10分钟内创建的任务
            ->whereIn('status', [0, 1, 2, 10]) // 0=等待执行, 1=排队中, 2=执行中, 10=任务恢复
            ->delete();

    }

    /**
     * 同步门店商品数据（获取门店、分类、商品并入库）
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function syncStoreGoodsData(Request $request)
    {
        set_time_limit(0); // 不限制执行时间
        ini_set('memory_limit', '512M'); // 设置内存限制

        // 获取参数
        $create_task = $request->param('create_task', 0, 'intval');
        $store_id = $request->param('store_id', 0, 'intval'); // 可以指定特定门店ID
        $batch_size = $request->param('batch_size', 1, 'intval'); // 分类批次大小参数
        $store_batch_size = $request->param('store_batch_size', 1, 'intval'); // 门店批次大小参数
        $one_store_per_city = $request->param('one_store_per_city', 0, 'intval'); // 是否每个城市只取一个门店
        
        // 如果需要创建定时任务
        if ($create_task) {
            $tokenInfo = $this->getValidToken();
            if (!$tokenInfo) {
                return $this->error('没有找到有效的token');
            }
            
            // 获取门店
            if ($store_id > 0) {
                // 指定了特定门店ID，优先级最高
                $storeQuery = Store::where('storeId', $store_id);
            } elseif ($one_store_per_city == 1) {
                // 每个城市只取一个门店
                $type = $request->param('type', 4, 'intval'); // 默认获取极速达门店(type=4)
                $status = $request->param('status', 1, 'intval'); // 默认获取正常状态门店
                
                // 构建SQL查询，使用子查询获取每个城市storeId最大的门店
                $subQuery = Store::where('type', $type)
                    ->where('status', $status)
                    ->where('city', '<>', '') // 排除城市为空的记录
                    ->field('MAX(storeId) as max_store_id, city')
                    ->group('city')
                    ->buildSql();
                    
                // 连接子查询，获取完整的门店信息
                $storeQuery = Store::alias('s')
                    ->join([$subQuery => 'sub'], 's.storeId = sub.max_store_id')
                    ->field('s.*');
            } else {
                // 默认获取需要更新的门店（更新时间超过更新间隔的门店）
                $currentTime = time();
                $storeQuery = Store::where('type', 4)
                    ->where('status', 1)
                    ->where('store_goods_count','>',200)
                    ->whereRaw('(update_time + update_interval) <= ?', [$currentTime])
                    ->order('update_time', 'asc')
                    ->limit(30);
            }
            $stores = $storeQuery->select();

            if ($stores->isEmpty()) {
                return $this->error('未找到可用的配送门店');
            }
            
            $tasksCreated = [];

            // 将门店分成多个批次
            $storesBatches = array_chunk($stores->toArray(), $store_batch_size);

            // 为每个门店批次创建一个任务
            foreach ($storesBatches as $batchIndex => $storesBatch) {
                // 提取当前批次的门店ID
                $batchStoreIds = array_column($storesBatch, 'storeId');

                // 检查当前门店是否已经有正在运行的任务
                $existingTasks = $this->checkExistingTasks($batchStoreIds);
                if (!empty($existingTasks)) {
//                    $this->debugLog('warning', '门店 ' . implode(',', $batchStoreIds) . ' 已有正在运行的任务，跳过创建: ' . implode(',', $existingTasks));
//                    $tasksCreated[] = [
//                        'batch_index' => $batchIndex,
//                        'store_ids' => $batchStoreIds,
//                        'batch_size' => $batch_size,
//                        'cron' => '',
//                        'status' => 'skipped',
//                        'reason' => '门店已有正在运行的任务: ' . implode(',', $existingTasks)
//                    ];
                    continue;
                }
                
                // 创建任务名称和描述
                $storeIdList = implode(',', $batchStoreIds);
                // 如果 $batchStoreIds 成员数 =1  使用门店id作为
                if (count($batchStoreIds) == 1) {
                    $taskName = "山姆门店{$batchStoreIds[0]}商品数据更新";
                    $taskDescription = "自动同步山姆会员店门店{$batchStoreIds[0]}商品数据，门店ID:{$storeIdList}";
                } else {
                    $taskName = "山姆门店批次{$batchIndex}商品数据更新";
                    $taskDescription = "自动同步山姆会员店批次{$batchIndex}商品数据，门店ID:{$storeIdList}";
                }

                // 设置任务参数
                $taskParams = [
                    'store_ids' => $batchStoreIds,
                    'batch_size' => $batch_size
                ];
                
                // 根据one_store_per_city参数决定是创建一次性任务还是定时任务
                $taskCreated = false;
                $cronExpression = '';
                if ($one_store_per_city == 1) {
                    // 如果是每城市一个门店模式，创建一次性任务
                    $cityNames = [];
                    foreach ($storesBatch as $store) {
                        if (!empty($store['city'])) {
                            $cityNames[] = $store['city'];
                        }
                    }
                    
                    $cityList = implode(',', array_unique($cityNames));
                    $taskName = "山姆每城市一店数据更新-批次{$batchIndex}";
                    $taskDescription = "一次性同步山姆会员店每城市一店数据";
                    
                    // 调用创建一次性任务的方法
                    $taskCreated = self::createTask(
                        $taskName, 
                        'SamsGoods',
                        'syncGoodsData', 
                        '0 0 * * *',  // 提供一个有效的cron表达式
                        $taskDescription,
                        $taskParams,
                        1  // 设置为执行一次
                    );
                } else {
                    // 正常模式，也改为创建单次任务
                    $cronExpression = '0 0 * * *';  // 提供一个有效的cron表达式

                    // 创建单次任务
                    $taskCreated = self::createTask(
                        $taskName,
                        'SamsGoods',
                        'syncGoodsData',
                        $cronExpression,
                        $taskDescription,
                        $taskParams,
                        1  // 设置为执行一次
                    );
                }
                
                $tasksCreated[] = [
                    'batch_index' => $batchIndex,
                    'store_ids' => $batchStoreIds,
                    'batch_size' => $batch_size,
                    'cron' => $cronExpression,
                    'status' => $taskCreated ? 'success' : 'failed'
                ];
            }
            
            return $this->success('创建商品数据定时任务完成', [
                'token_phone' => $tokenInfo['phone'],
                'tasks_created' => $tasksCreated
            ]);
        }
        
        try {
            $samClub = new SamClub();
            $statistics = [
                'stores' => 0,
                'categories' => 0,
                'products' => 0
            ];
            
            // 1. 获取门店
            if ($store_id > 0) {
                // 指定了特定门店ID，优先级最高
                $storeQuery = Store::where('storeId', $store_id);
            } elseif ($one_store_per_city == 1) {
                // 每个城市只取一个门店
                $type = $request->param('type', 4, 'intval'); // 默认获取极速达门店(type=4)
                $status = $request->param('status', 1, 'intval'); // 默认获取正常状态门店
                
                // 构建SQL查询，使用子查询获取每个城市storeId最大的门店
                $subQuery = Store::where('type', $type)
                    ->where('status', $status)
                    ->where('city', '<>', '') // 排除城市为空的记录
                    ->field('MAX(storeId) as max_store_id, city')
                    ->group('city')
                    ->buildSql();
                    
                // 连接子查询，获取完整的门店信息
                $storeQuery = Store::alias('s')
                    ->join([$subQuery => 'sub'], 's.storeId = sub.max_store_id')
                    ->field('s.*')
                    ->limit($store_batch_size); // 限制处理的门店数量，避免超时
            } else {
                // 默认获取需要更新的门店（更新时间超过更新间隔的门店）
                $currentTime = time();
                $storeQuery = Store::where('type', 4)
                    ->where('status', 1)
                    ->whereRaw('(update_time + update_interval) <= ?', [$currentTime])
                    ->order('update_time', 'asc')
                    ->limit(100); // 限制处理的门店数量，最多100个
            }
            
            $stores = $storeQuery->select();
                
            if ($stores->isEmpty()) {
                return $this->error('未找到可用的配送门店');
            }
            
            $statistics['stores'] = count($stores);
            
            // 2. 使用缓存获取分类数据，30分钟重新取一次
            $cacheKey = 'sams_categories_grouped';
            $categoriesData = cache($cacheKey);

            if (!$categoriesData) {
                // 缓存不存在或已过期，重新获取数据
                $allCategories = Categories::where('status', 1)->select()->toArray();

                if (empty($allCategories)) {
                    return $this->error('未找到分类数据');
                }

                // 将分类按层级分组
                $level1 = []; // 一级分类
                $level2 = []; // 二级分类
                $level3 = []; // 三级分类

                foreach ($allCategories as $category) {
                    switch ($category['level']) {
                        case 1:
                            $level1[$category['groupingId']] = $category;
                            break;
                        case 2:
                            $level2[$category['groupingId']] = $category;
                            break;
                        case 3:
                            $level3[$category['groupingId']] = $category;
                            break;
                    }
                }

                // 将分组后的数据存入缓存，30分钟过期
                $categoriesData = [
                    'level1' => $level1,
                    'level2' => $level2,
                    'level3' => $level3,
                    'total_count' => count($allCategories)
                ];
                cache($cacheKey, $categoriesData, 1800); // 1800秒 = 30分钟

                $this->debugLog('info', '分类数据已更新并缓存，总数: ' . count($allCategories));
            } else {
                // 使用缓存数据
                $level1 = $categoriesData['level1'];
                $level2 = $categoriesData['level2'];
                $level3 = $categoriesData['level3'];

                $this->debugLog('info', '使用缓存的分类数据，总数: ' . $categoriesData['total_count']);
            }

            $statistics['categories'] = $categoriesData['total_count'];
            
            // 在网页模式下，处理最多3个分类
            $maxCategories = 3;
            $processedCount = 0;
            $storeIds = array_column($stores->toArray(), 'storeId');
            
            // 3. 遍历三级分类，获取商品数据并入库
            foreach ($level3 as $categoryId => $category) {
                // 限制处理的分类数量
                if ($processedCount >= $maxCategories) {
                    break;
                }
                
                // 获取该分类下的商品
                $options = [
                    'frontCategoryIds' => [$categoryId], // 每次只处理一个分类ID
                    'pageSize' => 500,
                    'pageNum' => 1,
                    'useNewPage' => true,
                    'useNew' => true,
                ];
                
                // 获取同城市的type=2门店
                $type2StoreMap = $this->getSameCity2TypeStores($storeIds);
                
                // 构建门店信息列表，包含type=4和type=2门店
                $additionalStoreTypes = [];
                if (!empty($type2StoreMap)) {
                    // 合并所有城市的type=2门店ID
                    $type2StoreIds = [];
                    foreach ($type2StoreMap as $cityStoreIds) {
                        $type2StoreIds = array_merge($type2StoreIds, $cityStoreIds);
                    }
                    
                    if (!empty($type2StoreIds)) {
                        $additionalStoreTypes[2] = $type2StoreIds;
                    }
                }
                
                // 添加门店信息列表到请求选项
                $options['storeInfoVOList'] = $this->buildStoreInfoVOList($storeIds, $additionalStoreTypes);
                
                // 每次请求时获取新token
                $tokenInfo = $this->getValidToken();
                if (!$tokenInfo) {
                    $this->debugLog('error', '获取商品数据失败: 没有找到有效的token');
                    exit;
                }
                
                // 使用第一个门店ID作为请求参数
                $primaryStoreId = $storeIds[0];
                
                $result = $samClub->get_category_goods(
                    $primaryStoreId,
                    $tokenInfo['token'],
                    '',  // 空设备ID
                    $this->getProxyIp(),  // 代理IP
                    $options
                );
                
                $resultData = $this->handleApiResponse($result, "获取商品列表失败 (storeIds: " . implode(',', $storeIds) . ", categoryId: {$categoryId})", $tokenInfo['token']);
                
                if ($resultData && isset($resultData['data']['dataList']) && !empty($resultData['data']['dataList'])) {
                    // 获取分类ID
                    $catLv3Id = $categoryId;
                    $catLv2Id = 0;
                    $catLv1Id = 0;
                    
                    // 如果分类ID有效，尝试获取其父分类ID
                    if ($catLv3Id !== 0 && isset($level3[$catLv3Id])) {
                        $catLv2Id = $level3[$catLv3Id]['pid'] ?? 0;
                        if (isset($level2[$catLv2Id])) {
                            $catLv1Id = $level2[$catLv2Id]['pid'] ?? 0;
                        }
                    }
                    
                    // 为每个门店保存商品数据
                    foreach ($stores as $store) {
                        $savedCount = $this->saveGoodsData(
                            $resultData['data']['dataList'],
                            $store->toArray(),
                            $catLv3Id,
                            $catLv2Id,
                            $catLv1Id
                        );
                        
                        $statistics['products'] += $savedCount;
                    }
                }
                $processedCount++;
                // 避免频繁请求
                usleep(500000); // 休眠0.5秒
            }
            
            return $this->success('商品数据同步完成', $statistics);

        } catch (\Throwable $e) {
            $this->debugLog('error', '同步商品数据失败: ' . $e->getMessage());
            return $this->error('同步商品数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 保存商品数据到Goods模型
     * 
     * @param array $products 商品数据
     * @param array $store 门店数据
     * @param int $catLv3Id 三级分类ID
     * @param int $catLv2Id 二级分类ID
     * @param int $catLv1Id 一级分类ID
     * @return int 保存的商品数量
     */
    protected function saveGoodsData($products, $store, $catLv3Id = 0, $catLv2Id = 0, $catLv1Id = 0)
    {
        if (empty($products)) {
            return 0;
        }
        
        // 引入Goods模型和StoreGoods模型
        $goodsModel = new Goods();
        $storeGoodsModel = new StoreGoods();
        
        $now = time();
        $savedCount = 0;

        // 初始化批量操作数组
        $goodsUpdateData = [];
        $goodsInsertData = [];
        $storeGoodsUpdateData = [];
        $storeGoodsInsertData = [];

        // 提取所有商品ID并去重
        $spuIds = array_unique(array_column($products, 'spuId'));
        $spuIds = array_filter($spuIds); // 移除空值

        // 查询已有商品
        $existProducts = $goodsModel->whereIn('spuId', $spuIds)->select();

        // 构建查找映射
        $existMap = [];
        foreach ($existProducts as $product) {
            $existMap[$product['spuId']] = $product;
        }

        // 用于跟踪当前批次中已处理的spuId，避免重复插入
        $processedSpuIds = [];
        
        // 处理商品数据
        foreach ($products as $product) {
            // 检查必要字段
            if (!isset($product['spuId']) || empty($product['spuId'])) {
                continue;
            }

            $spuId = $product['spuId'];

            // 检查当前批次中是否已经处理过这个spuId
            if (isset($processedSpuIds[$spuId])) {
                $this->debugLog('warning', "跳过重复的spuId: {$spuId}");
                continue;
            }

            // 标记为已处理
            $processedSpuIds[$spuId] = true;
            
            // 提取价格信息
            $price = 0;

            if (isset($product['priceInfo']) && is_array($product['priceInfo'])) {
                foreach ($product['priceInfo'] as $priceItem) {
                    if ($priceItem['priceType'] == 1) { // 销售价
                        $price = intval($priceItem['price']); // 价格以分为单位，保存整数
                    }
                }
            }
            
            // 提取库存信息
            $stock = 0;
            if (isset($product['stockInfo']) && isset($product['stockInfo']['stockQuantity'])) {
                $stock = intval($product['stockInfo']['stockQuantity']);
            }

            // 提取商品categoryIdList
            $categoryIdList = [];
            if (isset($product['categoryIdList']) && is_array($product['categoryIdList'])) {
                $categoryIdList = $product['categoryIdList'];
            }
            $category1 = $categoryIdList[0] ?? 0;
            $category2 = $categoryIdList[1] ?? 0;
            $category3 = $categoryIdList[2] ?? 0;
            
            $seriesId = $product['seriesId'] ?? 0;
            $hostItemId = $product['hostItemId'] ?? 0;

            // 提取商品数据
            $goodsData = [
                'name' => $product['title'] ?? '',
                'spuId' => $spuId,
                'imgUrl' => isset($product['image']) ? $product['image'] : '',
                'status' => 1,
                'update_time' => $now
            ];
                        
            $map = [
                'catLv3'     => $catLv3Id,
                'catLv2'     => $catLv2Id,
                'catLv1'     => $catLv1Id,
                'seriesId'   => $seriesId,
                'hostItemId' => $hostItemId,
                'category1'  => $category1,
                'category2'  => $category2,
                'category3'  => $category3,
            ];
            
            foreach ($map as $key => $value) {
                if ($value !== 0) {
                    $goodsData[$key] = $value;
                }
            }
            
            // 获取商品中的storeId，如果存在的话
            $productStoreId = isset($product['storeId']) ? $product['storeId'] : $store['storeId'];
            
            // 保存门店商品关联数据
            $storeGoodsData = [
                'sam_store_id' => $productStoreId,
                'sam_goods_id' => $spuId,
                'price' => $price,
                'stock' => $stock,
                'status' => 1,
                'update_time' => $now
            ];
            
            // 查询该商品是否已在该门店存在
            $existStoreGoods = $storeGoodsModel->where('sam_store_id', $productStoreId)
                               ->where('sam_goods_id', $spuId)
                               ->find();

            // 检查商品是否已存在
            if (isset($existMap[$spuId])) {
                // 已有商品，加入批量更新数据
                $goodsUpdateData[] = array_merge(['id' => $existMap[$spuId]['id']], $goodsData);
            } else {
                // 新商品，加入批量插入数据
                $goodsData['create_time'] = $now;
                $goodsInsertData[] = $goodsData;
            }
            
            // 检查门店商品关联是否已存在
            if ($existStoreGoods) {
                // 已有关联，加入批量更新数据
                $storeGoodsUpdateData[] = array_merge(['id' => $existStoreGoods['id']], $storeGoodsData);
            } else {
                // 新关联，加入批量插入数据
                $storeGoodsData['create_time'] = $now;
                $storeGoodsInsertData[] = $storeGoodsData;
            }
            
            $savedCount++;
        }
        
        // 使用直接数据库操作，完全避免事务冲突
        try {
            // 批量更新商品 - 逐个更新
            if (!empty($goodsUpdateData)) {
                foreach ($goodsUpdateData as $updateData) {
                    Goods::where('id', $updateData['id'])->update($updateData);
                }
            }

            // 批量插入商品 - 使用insertAll，忽略重复键错误
            if (!empty($goodsInsertData)) {
                try {
                    Goods::insertAll($goodsInsertData);
                } catch (\think\db\exception\PDOException $e) {
                    // 如果是重复键错误，尝试逐个插入
                    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                        $this->debugLog('warning', "批量插入商品遇到重复键，尝试逐个插入");
                        foreach ($goodsInsertData as $goodsItem) {
                            try {
                                // 检查是否已存在
                                $exists = Goods::where('spuId', $goodsItem['spuId'])->find();
                                if (!$exists) {
                                    Goods::insert($goodsItem);
                                }
                            } catch (\Throwable $ex) {
                                $this->debugLog('error', "插入商品失败 spuId: {$goodsItem['spuId']}, 错误: " . $ex->getMessage());
                            }
                        }
                    } else {
                        throw $e;
                    }
                }
            }

            // 批量更新门店商品关联 - 逐个更新
            if (!empty($storeGoodsUpdateData)) {
                foreach ($storeGoodsUpdateData as $updateData) {
                    StoreGoods::where('id', $updateData['id'])->update($updateData);
                }
            }

            // 批量插入门店商品关联 - 使用insertAll，忽略重复键错误
            if (!empty($storeGoodsInsertData)) {
                try {
                    StoreGoods::insertAll($storeGoodsInsertData);
                } catch (\think\db\exception\PDOException $e) {
                    // 如果是重复键错误，尝试逐个插入
                    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                        $this->debugLog('warning', "批量插入门店商品关联遇到重复键，尝试逐个插入");
                        foreach ($storeGoodsInsertData as $storeGoodsItem) {
                            try {
                                // 检查是否已存在
                                $exists = StoreGoods::where('sam_store_id', $storeGoodsItem['sam_store_id'])
                                                   ->where('sam_goods_id', $storeGoodsItem['sam_goods_id'])
                                                   ->find();
                                if (!$exists) {
                                    StoreGoods::insert($storeGoodsItem);
                                }
                            } catch (\Throwable $ex) {
                                $this->debugLog('error', "插入门店商品关联失败 storeId: {$storeGoodsItem['sam_store_id']}, goodsId: {$storeGoodsItem['sam_goods_id']}, 错误: " . $ex->getMessage());
                            }
                        }
                    } else {
                        throw $e;
                    }
                }
            }

            return $savedCount;
        } catch (\Throwable $e) {
            $this->debugLog('error', "保存商品数据失败: " . $e->getMessage());
            throw $e; // 重新抛出异常，让调用方处理
        }
    }
    
    /**
     * 供计划任务调用的同步商品数据方法
     * 
     * @param string|array $params 包含参数的字符串或数组
     * @return bool 返回false将终止任务执行
     */
    public static function syncGoodsData($params = [])
    {
        try {
            $instance = new self();
            $samClub = new SamClub();
            $startTime = microtime(true);

            // 解析参数
            if (is_string($params)) {
                $params = json_decode($params, true) ?: [];
            } elseif (!is_array($params)) {
                $params = [];
            }

            // 获取门店ID数组
            $storeIds = [];
            if (isset($params['store_ids']) && is_array($params['store_ids'])) {
                $storeIds = array_map('intval', $params['store_ids']);
            } elseif (isset($params['store_id'])) {
                // 兼容旧版本，单个store_id
                $storeIds = [intval($params['store_id'])];
            }
            
            // 如果没有指定门店ID，直接返回失败
            if (empty($storeIds)) {
                self::staticDebugLog('error', '同步商品数据失败: 未指定门店ID');
                return false;
            }
            
            // 获取分类批次大小参数，默认为50
            $batchSize = isset($params['batch_size']) ? intval($params['batch_size']) : 50;
            // 确保批次大小至少为1
            $batchSize = max(1, $batchSize);
            
            // 获取门店信息
            $stores = Store::whereIn('storeId', $storeIds)->select();
            if ($stores->isEmpty()) {
                self::staticDebugLog('error', '同步商品数据失败: 未找到指定的门店');
                return false;
            }
            
            // 使用缓存获取分类数据，30分钟重新取一次
            $cacheKey = 'sams_categories_grouped';
            $categoriesData = Cache::get($cacheKey);

            if (!$categoriesData) {
                // 缓存不存在或已过期，重新获取数据
                $allCategories = Categories::where('status', 1)->select()->toArray();

                if (empty($allCategories)) {
                    self::staticDebugLog('error', '同步商品数据失败: 未找到分类数据');
                    return false;
                }

                // 将分类按层级分组
                $level1 = []; // 一级分类
                $level2 = []; // 二级分类
                $level3 = []; // 三级分类

                foreach ($allCategories as $category) {
                    switch ($category['level']) {
                        case 1:
                            $level1[$category['groupingId']] = $category;
                            break;
                        case 2:
                            $level2[$category['groupingId']] = $category;
                            break;
                        case 3:
                            $level3[$category['groupingId']] = $category;
                            break;
                    }
                }

                // 将分组后的数据存入缓存，30分钟过期
                $categoriesData = [
                    'level1' => $level1,
                    'level2' => $level2,
                    'level3' => $level3,
                    'total_count' => count($allCategories)
                ];
                Cache::set($cacheKey, $categoriesData, 1800); // 1800秒 = 30分钟

                self::staticDebugLog('info', '分类数据已更新并缓存，总数: ' . count($allCategories));
            } else {
                // 使用缓存数据
                $level1 = $categoriesData['level1'];
                $level2 = $categoriesData['level2'];
                $level3 = $categoriesData['level3'];

                self::staticDebugLog('info', '使用缓存的分类数据，总数: ' . $categoriesData['total_count']);
            }
            
            // 使用array_chunk将所有三级分类分成多个批次
            $categoriesBatches = array_chunk($level3, $batchSize);
            
            // 统计信息
            $storeNames = array_column($stores->toArray(), 'name');
            $statistics = [
                'total_categories' => count($level3),
                'batch_count' => count($categoriesBatches),
                'processed_products' => 0,
                'start_time' => date('Y-m-d H:i:s')
            ];
            
            self::staticDebugLog('info', "开始同步商品数据 | 门店: " . implode(',', $storeNames) . " | 分类总数: {$statistics['total_categories']} | 批次数: {$statistics['batch_count']} | 批次大小: {$batchSize}");
            
            // 遍历分类批次
            foreach ($categoriesBatches as $batchIndex => $categoriesBatch) {
                $batchStartTime = microtime(true);
                
                // 收集当前批次的所有分类ID
                $categoryIds = array_column($categoriesBatch, 'groupingId');
                
                // 获取该批次分类下的商品
                $options = [
                    'frontCategoryIds' => $categoryIds,
                    'pageSize' => 500,
                    'pageNum' => 1,
                    'useNewPage' => true,
                    'useNew' => true,
                ];
                
                // 获取同城市的type=2门店
                $type2StoreMap = $instance->getSameCity2TypeStores($storeIds);
                
                // 构建门店信息列表，包含type=4和type=2门店
                $additionalStoreTypes = [];
                if (!empty($type2StoreMap)) {
                    // 合并所有城市的type=2门店ID
                    $type2StoreIds = [];
                    foreach ($type2StoreMap as $cityStoreIds) {
                        $type2StoreIds = array_merge($type2StoreIds, $cityStoreIds);
                    }
                    
                    if (!empty($type2StoreIds)) {
                        $additionalStoreTypes[2] = $type2StoreIds;
                    }
                }
                
                // 添加门店信息列表到请求选项
                $options['storeInfoVOList'] = $instance->buildStoreInfoVOList($storeIds, $additionalStoreTypes);
                
                // 设置最大重试次数
                $maxRetries = 3;
                $retryCount = 0;
                $success = false;
                $resultData = null;
                
                // 重试逻辑
                while ($retryCount < $maxRetries && !$success) {
                    // 每次请求时获取新token
                    $tokenInfo = $instance->getValidToken();
                    if (!$tokenInfo) {
                        self::staticDebugLog('error', "门店：". implode(',', $storeNames) ."批次 " . ($batchIndex + 1) . "/" . count($categoriesBatches) . " 处理失败: 没有找到有效的token");
                        return false; // 获取token失败直接停止任务
                    }
                    
                    // 使用第一个门店ID作为请求参数
                    $primaryStoreId = $storeIds[0];
                    
                    try {
                        $result = $samClub->get_category_goods(
                            $primaryStoreId,
                            $tokenInfo['token'],
                            '',  // 空设备ID
                            $instance->getProxyIp(),  // 代理IP
                            $options
                        );
                        
                        $resultData = $instance->handleApiResponse($result, "批次 " . ($batchIndex + 1) . "/" . count($categoriesBatches) . " 获取商品失败", $tokenInfo['token']);
                        
                        if ($resultData && isset($resultData['data']['dataList'])) {
                            $success = true; // 成功获取数据
                            break; // 跳出重试循环
                        } else {
                            $retryCount++;
                            if ($retryCount < $maxRetries) {
                                self::staticDebugLog('warning', "门店：". implode(',', $storeNames) ."批次 " . ($batchIndex + 1) . "/" . count($categoriesBatches) . " 获取数据失败，准备重试 ({$retryCount}/{$maxRetries})");
                                sleep(2); // 休眠2秒后重试
                            }
                        }
                    } catch (\Throwable $e) {
                        $retryCount++;
                        if ($retryCount < $maxRetries) {
                            self::staticDebugLog('warning', "门店：". implode(',', $storeNames) ."批次 " . ($batchIndex + 1) . "/" . count($categoriesBatches) . " 请求异常: " . $e->getMessage() . "，准备重试 ({$retryCount}/{$maxRetries})");
                            sleep(2); // 休眠2秒后重试
                        } else {
                            self::staticDebugLog('error', "门店：". implode(',', $storeNames) ."批次 " . ($batchIndex + 1) . "/" . count($categoriesBatches) . " 达到最大重试次数，放弃处理: " . $e->getMessage());
                        }
                    }
                }
                
                if ($success && isset($resultData['data']['dataList']) && !empty($resultData['data']['dataList'])) {
                    $productCount = count($resultData['data']['dataList']);
                    $statistics['processed_products'] += $productCount;
                    
                    // 如果是多个三级分类ID，则使用0作为分类ID
                    $catLv3Id = count($categoryIds) > 1 ? 0 : $categoryIds[0];
                    $catLv2Id = 0;
                    $catLv1Id = 0;
                    
                    // 如果只有一个分类ID，尝试获取其父分类ID
                    if ($catLv3Id !== 0 && isset($level3[$catLv3Id])) {
                        $catLv2Id = $level3[$catLv3Id]['pid'] ?? 0;
                        if (isset($level2[$catLv2Id])) {
                            $catLv1Id = $level2[$catLv2Id]['pid'] ?? 0;
                        }
                    }
                    
                    // 为每个门店保存商品数据
                    foreach ($stores as $store) {
                        $instance->saveGoodsData(
                            $resultData['data']['dataList'],
                            $store->toArray(),
                            $catLv3Id,
                            $catLv2Id,
                            $catLv1Id
                        );
                    }
                    
                    $batchTime = round(microtime(true) - $batchStartTime, 2);

                    self::staticDebugLog('info', "门店：". implode(',', $storeNames) ."批次 " . ($batchIndex + 1) . "/" . count($categoriesBatches) . " 处理完成 | 商品数: {$productCount} | 耗时: {$batchTime}秒");
                } else {
                    self::staticDebugLog('warning', "门店：". implode(',', $storeNames) ."批次 " . ($batchIndex + 1) . "/" . count($categoriesBatches) . " 未获取到商品数据，已重试 {$retryCount} 次");
                }
                
                // 避免频繁请求
                usleep(500000); // 休眠0.5秒
            }
            
            $totalTime = round(microtime(true) - $startTime, 2);
            self::staticDebugLog('info', "门店：". implode(',', $storeNames) ."商品数据同步完成 | 总处理商品: {$statistics['processed_products']} | 总耗时: {$totalTime}秒");

            // 同步完成后，处理未分类的商品数据
            try {
                $uncategorizedCount = $instance->processUncategorizedGoods();
                self::staticDebugLog('info', "门店：". implode(',', $storeNames) ."未分类商品处理完成 | 处理数量: {$uncategorizedCount}");
            } catch (\Throwable $e) {
                self::staticDebugLog('error', "门店：". implode(',', $storeNames) ."处理未分类商品失败: " . $e->getMessage());
            }

            // 同步完成后，为每个门店整理今天更新过的商品数据并上传到OSS
            foreach ($stores as $store) {
                try {
                    $storeId = $store['storeId'];
                    $instance->exportStoreProductsToOSS($storeId);
                } catch (\Throwable $e) {
                    self::staticDebugLog('error', "导出门店 {$storeId} 商品数据失败: " . $e->getMessage());
                }
            }

            return true;
        } catch (\Throwable $e) {
            self::staticDebugLog('error', '同步商品数据失败: ' . $e->getMessage() . ' | 位置: ' . $e->getFile() . ':' . $e->getLine());
            return false;
        }
    }

    /**
     * 构建门店信息列表参数
     * 
     * @param array|int $storeIds 门店ID数组或单个门店ID
     * @param array $additionalStoreTypes 额外的门店类型配置，格式为 [type => storeId]
     * @return array 门店信息列表
     */
    protected function buildStoreInfoVOList($storeIds, $additionalStoreTypes = [])
    {
        $storeInfoList = [];
        
        // 如果传入的是单个ID，转换为数组
        if (!is_array($storeIds)) {
            $storeIds = [$storeIds];
        }
        
        // 为每个门店ID创建一个门店信息（默认type=4）
        foreach ($storeIds as $storeId) {
            $storeInfoList[] = ["storeType" => 4, "storeId" => $storeId, "storeDeliveryAttr" => [1,3,4,6,14,12,5,2,7,13]];
        }
        // 如果$storeIds  > 1 就加入下面的额外门店
        if (count($storeIds) > 1) {
            // 处理额外的门店类型
            if (!empty($additionalStoreTypes)) {
                foreach ($additionalStoreTypes as $type => $typeStoreIds) {
                    if (!is_array($typeStoreIds)) {
                        $typeStoreIds = [$typeStoreIds];
                    }

                    foreach ($typeStoreIds as $storeId) {
                        // 根据不同门店类型设置不同的配送属性
                        $deliveryAttr = [1,3,4,6,14,12,5,2,7,13]; // 默认配送属性

                        switch ($type) {
                            case 2:
                                $deliveryAttr = [3,4,6,14,5,12,2,7,13];
                                break;
                            case 8:
                                $deliveryAttr = [1];
                                break;
                            case 256:
                                $deliveryAttr = [9,13];
                                break;
                        }
                        $storeInfoList[] = ["storeType" => $type, "storeId" => $storeId, "storeDeliveryAttr" => $deliveryAttr];
                    }
                }
            }
            // 遍历 storeType ，如果没有 8 和256的门店则添加
            $storeTypeList = array_column($storeInfoList, 'storeType');
            if (!in_array(8, $storeTypeList)) {
                $storeInfoList[] = ["storeType" => 8, "storeId" => 9992, "storeDeliveryAttr" => [1]];
            }
            if (!in_array(256, $storeTypeList)) {
                $storeInfoList[] = ["storeType" => 256, "storeId" => 6758, "storeDeliveryAttr" => [9,13]];
            }
        }
        return $storeInfoList;
    }
    
    /**
     * 获取每个城市的一个门店（按storeId降序）
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function getOnlyOneStorePerCity(Request $request)
    {
        try {
            // 获取参数
            $type = $request->param('type', 4, 'intval'); // 默认获取极速达门店(type=4)
            $status = $request->param('status', 1, 'intval'); // 默认获取正常状态门店
            
            // 构建SQL查询，使用子查询获取每个城市storeId最大的门店
            $subQuery = Store::where('type', $type)
                ->where('status', $status)
                ->where('city', '<>', '') // 排除城市为空的记录
                ->field('MAX(storeId) as max_store_id, city')
                ->group('city')
                ->buildSql();
                
            // 连接子查询，获取完整的门店信息
            $stores = Store::alias('s')
                ->join([$subQuery => 'sub'], 's.storeId = sub.max_store_id')
                ->field('s.*')
                ->select();
                
            if ($stores->isEmpty()) {
                return $this->error('未找到符合条件的门店');
            }
            
            // 统计信息
            $statistics = [
                'total_cities' => count($stores),
                'store_ids' => array_column($stores->toArray(), 'storeId'),
                'cities' => array_column($stores->toArray(), 'city')
            ];
            
            return $this->success('获取每个城市的一个门店成功', [
                'statistics' => $statistics,
                'stores' => $stores
            ]);
            
        } catch (\Throwable $e) {
            $this->debugLog('error', '获取每个城市的一个门店失败: ' . $e->getMessage() . ' | 位置: ' . $e->getFile() . ':' . $e->getLine());
            return $this->error('获取每个城市的一个门店失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取与指定门店同城市的type=2门店
     * 
     * @param int|array $storeIds 门店ID或ID数组
     * @return array 同城市的type=2门店ID映射 [城市 => type2门店ID数组]
     */
    protected function getSameCity2TypeStores($storeIds)
    {
        if (empty($storeIds)) {
            return [];
        }
        
        // 如果传入的是单个ID，转换为数组
        if (!is_array($storeIds)) {
            $storeIds = [$storeIds];
        }
        // 判断有没有缓存 如果有 就取缓存
        $cacheKey = 'sams_same_city_2_type_stores_' . md5(json_encode($storeIds));
        $cityStoreMap = Cache::get($cacheKey);
        if ($cityStoreMap) {
            return $cityStoreMap;
        }
        
        try {
            // 获取指定门店的城市信息
            $stores = Store::whereIn('storeId', $storeIds)->select();
            if ($stores->isEmpty()) {
                return [];
            }
            
            // 提取城市列表
            $cities = [];
            foreach ($stores as $store) {
                if (!empty($store['city'])) {
                    $cities[] = $store['city'];
                }
            }
            
            if (empty($cities)) {
                return [];
            }
            
            // 查询这些城市中的type=2门店
            $type2Stores = Store::where('type', 2)
                ->where('status', 1)
                ->whereIn('city', $cities)
                ->select();
                
            if ($type2Stores->isEmpty()) {
                return [];
            }
            
            // 按城市分组
            $cityStoreMap = [];
            foreach ($type2Stores as $store) {
                if (!empty($store['city'])) {
                    if (!isset($cityStoreMap[$store['city']])) {
                        $cityStoreMap[$store['city']] = [];
                    }
                    $cityStoreMap[$store['city']][] = $store['storeId'];
                }
            }

            // 设置三小时缓存，已门店列表md5设置
            $cacheKey = 'sams_same_city_2_type_stores_' . md5(json_encode($storeIds));
            Cache::set($cacheKey, $cityStoreMap, 10800);


            return $cityStoreMap;
        } catch (\Throwable $e) {
            $this->debugLog('error', '获取同城市type=2门店失败: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 导出门店商品数据到OSS
     * 
     * @param int $storeId 门店ID
     * @return bool 是否导出成功
     * @throws \Exception
     */
    protected function exportStoreProductsToOSS($storeId)
    {
        // 引入必要的类
        $ossClient = null;
        
        try {
            // 获取该门店今天更新过的商品信息
            $today = date('Y-m-d');
            $storeGoodsModel = new StoreGoods();
            $goodsModel = new Goods();
            $categoriesModel = new Categories();
            
            $products = $storeGoodsModel->where('sam_store_id', $storeId)
                ->where('update_time', '>=', strtotime($today))
                ->select();
            
            if ($products->isEmpty()) {
                $this->debugLog('info', "门店ID:{$storeId}没有今天更新的商品数据需要导出");
                return false;
            }

            // 更新 门店更新时间
            Store::where('storeId', $storeId)->update(['update_time' => time()]);

            // 获取所有分类信息，以便查找分类名称
            $categoryNames = [];
            $categories = $categoriesModel->select();
            foreach ($categories as $category) {
                $categoryNames[$category['groupingId']] = $category['name'];
            }
            
            // 定义文件内容变量
            $fileContent = '';
            
            foreach ($products as $product) {
                // 获取商品信息
                $skuId = $product['sam_goods_id'];
                $goods = $goodsModel->where('spuId', $skuId)->find();
                
                if (!$goods) {
                    continue; // 如果没有商品信息，则跳过
                }
                
                $upc = '';
                
                // 处理hostUpc数据
                if (isset($goods['hostUpc']) && !empty($goods['hostUpc'])) {
                    $hostUpc = $goods['hostUpc'];
                    if (is_string($hostUpc)) {
                        $hostUpc = json_decode($hostUpc, true);
                    }
                    
                    if (is_array($hostUpc) && !empty($hostUpc)) {
                        foreach ($hostUpc as $value) {
                            if (isset($value["value"])) {
                                $upc = $value["value"];
                                break;
                            }
                        }
                    }
                }
                
                // 计算 itemId (与skuId相同)
                $itemId = $skuId;
                
                // 获取分类名称
                $catLv1Name = isset($categoryNames[$goods['catLv1']]) ? $categoryNames[$goods['catLv1']] : '未知';
                $catLv2Name = isset($categoryNames[$goods['catLv2']]) ? $categoryNames[$goods['catLv2']] : '未知';
                
                // 构造商品数据（JSON 格式）
                $productData = [
                    'itemId'        => $itemId,
                    'skuId'         => $skuId,
                    'name'          => $goods['name'],
                    'imgUrl'        => $goods['imgUrl'],
                    'sales'         => 0, // 假设 sales 即为库存数量
                    'stock'         => $product['stock'],
                    'storeId'       => $product['sam_store_id'],
                    'catLv1'        => $goods['catLv1'],
                    'catLv1Name'    => $catLv1Name,
                    'catLv2'        => $goods['catLv2'],
                    'catLv2Name'    => $catLv2Name,
                    'originalPrice' => $product['price'],
                    'realtimePrice' => $product['price'],
                    'upc'           => $upc
                ];
                
                // 将数据转换为 JSON 格式并添加到文件内容中
                $fileContent .= json_encode($productData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . "\n";
            }
            
            // 获取当前时间戳并格式化为：20241028120158
            $datetime = date('YmdHis');
            
            // 构造文件名和保存路径
            $fileName = "SAM-APP-{$storeId}-{$datetime}";
            $filePath = public_path('sam_data') . '/' . $fileName;
            
            // 确保目录存在，如果不存在则创建
            if (!is_dir(public_path('sam_data'))) {
                mkdir(public_path('sam_data'), 0777, true);
            }
            
            // 保存为 txt 文件
            file_put_contents($filePath, $fileContent);
            // 设置文件777权限
            chmod($filePath, 0777);
            
            // 上传到OSS
            $this->uploadToOSS($filePath, $fileName, $storeId);

            $this->debugLog('info', "门店ID:{$storeId}的商品数据已导出并上传到OSS，文件名: {$fileName},数量：{$products->count()}");
            return true;
        } catch (\Throwable $e) {
            $this->debugLog('error', "导出门店 {$storeId} 商品数据失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 上传文件到OSS并发送回调
     * 
     * @param string $filePath 本地文件路径
     * @param string $fileName 文件名
     * @param int $storeId 门店ID
     * @return bool 是否上传成功
     */
    protected function uploadToOSS($filePath, $fileName, $storeId)
    {
        try {
            // OSS配置信息
            $accessKeyId = 'LTAI5tBU2qVoCyqCPuQrm8og';
            $accessKeySecret = '******************************';
            $endpoint = "http://oss-cn-shanghai.aliyuncs.com";
            $bucket = "prodprovider3";
            
            // 引入OSS相关类

            $provider = new StaticCredentialsProvider($accessKeyId, $accessKeySecret);
            
            $config = array(
                "provider" => $provider,
                "endpoint" => $endpoint,
                "signatureVersion" => OssClient::OSS_SIGNATURE_VERSION_V4,
                "region" => "cn-shanghai"
            );
            
            $ossClient = new OssClient($config);
            
            // 上传文件
            $ossClient->uploadFile($bucket, $fileName, $filePath);
            
            // 发送回调通知
            $post_data = [
                "success" => true,
                "callbackUrl" => "https://c.dalingmaicai.com/api/productdataproc/callback",
                "storeId" => $storeId,
                "dataFileUrl" => "https://prodprovider3.oss-cn-shanghai.aliyuncs.com/{$fileName}",
                "bizDataId" => "PRODUCTLIST",
                "channel" => "SAM",
                "platform" => "APP",
                "taskId" => md5($fileName),
                "timeStamp" => time()
            ];
            
            $post_data = json_encode($post_data);
            
            // 使用SDK_Http类中的http_curl方法
            $sdkHttp = new \app\common\model\SDK_Http();
            $callback_res = $sdkHttp->http_curl('http://spider.otavia.com.cn/productDataProc/callback', ['post' => $post_data]);
            
            $this->debugLog('info', "[回调结果] {$callback_res}");
            
            // 使用微信推送通知
            $domain = request()->domain();
            $message = "[新版更新库存完成] \n门店ID: {$storeId} \n文件名: {$fileName} \n \n下载地址: {$domain}/sam_data/{$fileName} \n \n回调结果：{$callback_res} \n完成时间: " . date("Y-m-d H:i:s");
            
            // 使用已有的wxPush公共函数
            wxPush($message);
            
            return true;
        } catch (OssException $e) {
            $this->debugLog('error', "OSS上传失败: " . $e->getMessage());
            return false;
        } catch (\Throwable $e) {
            $this->debugLog('error', "上传文件到OSS失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理未分类的商品数据
     *
     * @return int 处理的商品数量
     */
    protected function processUncategorizedGoods()
    {
        try {
            // 查询所有未分类商品（分类为0或空或NULL）
            $goods = Goods::where('status', 1)
                ->where(function($query) {
                    $query->whereRaw('catLv1 = 0 OR catLv1 = "" OR catLv1 IS NULL');
                })
                ->where(function($query) {
                    $query->whereRaw('catLv2 = 0 OR catLv2 = "" OR catLv2 IS NULL');
                })
                ->where(function($query) {
                    $query->whereRaw('catLv3 = 0 OR catLv3 = "" OR catLv3 IS NULL');
                })
                ->field('id, spuId, name, category1, category2, category3')
                ->select();

            if ($goods->isEmpty()) {
                $this->debugLog('info', '没有找到未分类商品');
                return 0;
            }

            $updateCount = 0;

            // 遍历未分类商品并处理
            foreach ($goods as $item) {
                // 获取category1、category2、category3
                $category1 = $item->category1 ?: '';
                $category2 = $item->category2 ?: '';
                $category3 = $item->category3 ?: '';

                // 通过category1、category2、category3查找对应的商品，取相同最多的catLv1、catLv2和catLv3更新
                // 使用SamsGoodsDetail中已有的方法避免重复代码
                $goodsDetailInstance = new \app\api\controller\Sams\SamsGoodsDetail();
                $updateInfo = $goodsDetailInstance->updateCategoryLevels($item->id, $category1, $category2, $category3);

                if ($updateInfo['updated']) {
                    $updateCount++;
                    $this->debugLog('info', "商品分类更新成功 | ID: {$item->id} | spuId: {$item->spuId} | catLv1: {$updateInfo['catLv1']} | catLv2: {$updateInfo['catLv2']} | catLv3: {$updateInfo['catLv3']}");
                }
            }

            return $updateCount;
        } catch (\Throwable $e) {
            $this->debugLog('error', '处理未分类商品失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 检查门店是否已有正在运行的任务
     *
     * @param array $storeIds 门店ID数组
     * @return array 正在运行的任务名称数组
     */
    protected function checkExistingTasks($storeIds)
    {
        try {
            $existingTasks = [];

            // 将 status  add_time > 当前时间 3个小时的，也就是3个小时创建的的任务 status=1 的全部删除
            $threeHoursAgo = time() - 3 * 3600;
            \app\admin\model\Plantask::where('add_time', '>=', $threeHoursAgo)
                ->where('status', 1)
                ->delete();

            // 查询最近10分钟内创建的山姆商品相关任务
            $tenMinutesAgo = time() - 600;
            $runningTasks = \app\admin\model\Plantask::where('task_name', 'like', '山姆%商品%')
                ->where('add_time', '>=', $tenMinutesAgo) // 最近10分钟内创建的任务
                ->whereIn('status', [0, 1, 2, 10]) // 0=等待执行, 1=排队中, 2=执行中, 10=任务恢复
                ->select();

            if ($runningTasks->isEmpty()) {
                return [];
            }

            // 检查每个正在运行的任务是否包含当前门店
            foreach ($runningTasks as $task) {
                try {
                    // 从备注中提取门店ID
                    $taskStoreIds = [];
                    if (!empty($task->remark)) {
                        // 使用正则表达式从备注中提取门店ID
                        if (preg_match('/门店ID:([0-9,]+)/', $task->remark, $matches)) {
                            $storeIdString = $matches[1];
                            $taskStoreIds = array_map('intval', explode(',', $storeIdString));
                        }
                    }

                    // 如果备注中没有找到门店ID，尝试从参数中获取（兼容旧任务）
                    if (empty($taskStoreIds)) {
                        $params = json_decode($task->params, true);
                        if ($params && isset($params[0])) {
                            $taskParams = $params[0];
                            if (isset($taskParams['store_ids']) && is_array($taskParams['store_ids'])) {
                                $taskStoreIds = $taskParams['store_ids'];
                            } elseif (isset($taskParams['store_id'])) {
                                $taskStoreIds = [$taskParams['store_id']];
                            }
                        }
                    }

                    // 检查是否有重叠的门店
                    if (!empty($taskStoreIds)) {
                        $overlap = array_intersect($storeIds, $taskStoreIds);
                        if (!empty($overlap)) {
                            $existingTasks[] = $task->task_name . '(ID:' . $task->task_id . ',门店:' . implode(',', $overlap) . ')';
                        }
                    }
                } catch (\Throwable $e) {
                    $this->debugLog('warning', '解析任务信息失败: ' . $e->getMessage());
                    continue;
                }
            }

            return $existingTasks;
        } catch (\Throwable $e) {
            $this->debugLog('error', '检查现有任务失败: ' . $e->getMessage());
            return [];
        }
    }

}