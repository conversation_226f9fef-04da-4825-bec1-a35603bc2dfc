<?php

namespace app\api\controller\Sams;

use app\common\model\SamClub;
use app\common\model\sam\Token;
use modules\plantask\library\Task;
use think\facade\Log;
use think\Response;


/**
 * 山姆会员店数据采集基础类
 */
class SamsBase
{
    // 默认配置
    protected $defaultConfig = [
        'device_id' => '',
        'ip' => ''
    ];

    /**
     * 调试模式日志记录
     * 只在调试模式下记录日志
     *
     * @param string $level 日志级别 (info, error, warning, debug, alert)
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    protected function debugLog($level, $message, $context = [])
    {
        // 检查是否为调试模式
        if (!$this->isDebugMode()) {
            return;
        }

        // 根据级别调用对应的日志方法
        switch (strtolower($level)) {
            case 'error':
                Log::error($message, $context);
                break;
            case 'warning':
                Log::warning($message, $context);
                break;
            case 'debug':
                Log::debug($message, $context);
                break;
            case 'alert':
                Log::alert($message, $context);
                break;
            case 'info':
            default:
                Log::info($message, $context);
                break;
        }
    }

    /**
     * 检查是否为调试模式
     *
     * @return bool
     */
    protected function isDebugMode()
    {
        // 方式1: 检查环境变量
        if (getenv('SAMS_DEBUG') === 'true') {
            return true;
        }

        // 方式2: 检查配置文件
        if (config('app.debug') === true) {
            return true;
        }

        // 方式3: 检查系统配置（如果有的话）
        if (function_exists('get_sys_config') && get_sys_config('sams_debug_mode') == 1) {
            return true;
        }

        return false;
    }

    /**
     * 静态版本的调试模式日志记录
     * 只在调试模式下记录日志
     *
     * @param string $level 日志级别 (info, error, warning, debug, alert)
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    protected static function staticDebugLog($level, $message, $context = [])
    {
        // 检查是否为调试模式
        if (!self::staticIsDebugMode()) {
            return;
        }

        // 根据级别调用对应的日志方法
        switch (strtolower($level)) {
            case 'error':
                Log::error($message, $context);
                break;
            case 'warning':
                Log::warning($message, $context);
                break;
            case 'debug':
                Log::debug($message, $context);
                break;
            case 'alert':
                Log::alert($message, $context);
                break;
            case 'info':
            default:
                Log::info($message, $context);
                break;
        }
    }

    /**
     * 静态版本的检查是否为调试模式
     *
     * @return bool
     */
    protected static function staticIsDebugMode()
    {
        // 方式1: 检查环境变量
        if (getenv('SAMS_DEBUG') === 'true') {
            return true;
        }

        // 方式2: 检查配置文件
        if (config('app.debug') === true) {
            return true;
        }

        // 方式3: 检查系统配置（如果有的话）
        if (function_exists('get_sys_config') && get_sys_config('sams_debug_mode') == 1) {
            return true;
        }

        return false;
    }

    /**
     * 获取代理IP配置
     * @return string
     */
    protected function getProxyIp()
    {
        return get_sys_config('sam_proxy_ip');
    }
    
    /**
     * 返回成功的JSON响应
     */
    protected function success($msg = '', $data = null, $code = 200)
    {
        return Response::create([
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
            'time' => time()
        ], 'json');
    }
    
    /**
     * 返回错误的JSON响应
     */
    protected function error($msg = '', $data = null, $code = 0)
    {
        return Response::create([
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
            'time' => time()
        ], 'json');
    }
    
    /**
     * 获取有效的Token
     * 
     * @return array|false 返回token和手机号，或者失败时返回false
     */
    protected function getValidToken()
    {
        $tokenModel = Token::where('status', 1)->where('usage_count', '<', 1200)->order('usage_count', 'desc')->find();
        if (!$tokenModel) {
            return false;
        }


        
        // 获取token信息
        $tokenInfo = [
            'token' => $tokenModel->token,
            'phone' => $tokenModel->phone ?? ''
        ];
        
        // 检查是否需要调用login_success
        $this->refreshTokenSession($tokenInfo['token']);
        
        return $tokenInfo;
    }
    
    /**
     * 刷新token会话，每小时调用一次login_success
     * 
     * @param string $token 需要刷新的token
     * @return bool 是否成功刷新
     */
    protected function refreshTokenSession($token)
    {
        try {
            // 使用缓存检查上次刷新时间，键名使用token的md5值
            $cacheKey = 'token_refresh_' . md5($token);
            $lastRefreshTime = cache($cacheKey);
            
            // 如果缓存不存在或者已经过期（超过1小时）
            if ($lastRefreshTime === null || (time() - $lastRefreshTime) > 3600) {
                // 创建SamClub实例
                $samClub = new SamClub();

                // 调用login_success方法
                $samClub->login_success($token, '', $this->getProxyIp());
                
                // 更新缓存，记录当前时间
                cache($cacheKey, time(), 3600); // 缓存1小时
                
                $this->debugLog('info', "Token会话已刷新: " . substr($token, 0, 10) . '...');
                return true;
            }
            
            return false;
        } catch (\Throwable $e) {
            $this->debugLog('error', '刷新Token会话失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 通用的创建任务方法
     *
     * @param string $taskName 任务名称
     * @param string $controllerName 控制器名称
     * @param string $method 任务执行的方法名
     * @param string $cron cron表达式
     * @param string $remark 备注
     * @param array $params 传递给任务的参数
     * @param int $repeat_times 重复次数(0=无限循环, 1=执行一次, n=执行n次)
     * @return bool 是否创建成功
     */
    protected static function createTask($taskName, $controllerName, $method, $cron, $remark, $params = [], $repeat_times = 0)
    {
        try {
            Task::add(
                task_name: $taskName,
                type: 'method',
                goal: "\\app\\api\\controller\\Sams\\{$controllerName}@{$method}",
                params: json_encode([$params]),
                repeat_times: $repeat_times,
                rule: $cron,
                remark: $remark
            );

            return true;
        } catch (\Throwable $e) {
            self::staticDebugLog('error', "创建任务 '{$taskName}' 失败: " . $e->getMessage() . $cron );
            return false;
        }
    }
    
    /**
     * 通用的API请求处理方法
     * 
     * @param string $result API返回的JSON字符串
     * @param string $errorMsg 错误信息前缀
     * @param string $token 使用的token，用于在鉴权失败时禁用
     * @param bool $stopTask 是否在触发反爬机制时停止任务，默认为true
     * @return array|false 成功时返回解析后的数据，失败时返回false
     */
    protected function handleApiResponse($result, $errorMsg = '接口请求失败', $token = '', $stopTask = true)
    {
        // 检查原始返回内容是否包含限制请求的关键词
        $blockKeywords = ['自动阻断请求', '请求太频繁', '触发反爬虫机制', '访问受限'];
        if (is_string($result) && $this->containsAny($result, $blockKeywords)) {
            Log::error("{$errorMsg}: 请求被限制，触发反爬机制: " . $result);
            
            // 如果需要停止任务
            if ($stopTask) {
                // 记录严重错误
                Log::alert("检测到反爬机制触发，停止所有相关任务");
                
                try {
                    // 使用任务库停止相关任务
                    $this->stopAllRelatedTasks();
                } catch (\Throwable $e) {
                    Log::error("停止任务失败: " . $e->getMessage());
                }
            }
            
            return false;
        }
        
        $resultData = json_decode($result, true);
        
        // 检查返回数据是否有效
        if (!$resultData) {
            Log::error("{$errorMsg}: 无效的返回数据: " . substr($result, 0, 200));
            return false;
        }
        
        // 检查是否返回了错误
        if (isset($resultData['success']) && $resultData['success'] === false) {
            $errorCode = $resultData['code'] ?? '未知错误码';
            $errorMessage = $resultData['msg'] ?? '未知错误';
            $requestId = $resultData['requestId'] ?? '';
            $traceId = $resultData['traceId'] ?? '';
            
            // 检查是否包含请求频率限制相关的错误信息
            $frequencyLimitKeywords = ['请求太频繁', '触发反爬虫', '访问受限'];
            if ($this->containsAny($errorMessage, $frequencyLimitKeywords) || $errorCode === 'FREQUENCY_LIMITED') {
                Log::error("{$errorMsg}: 请求频率受限: [{$errorCode}] {$errorMessage}");
                
                // 如果需要停止任务
                if ($stopTask) {
                    // 记录严重错误
                    Log::alert("检测到频率限制，停止所有相关任务");
                    
                    try {
                        // 使用任务库停止相关任务
                        $this->stopAllRelatedTasks();
                    } catch (\Throwable $e) {
                        Log::error("停止任务失败: " . $e->getMessage());
                    }
                }
                
                return false;
            }
            
            Log::error("{$errorMsg}: [{$errorCode}] {$errorMessage}, requestId: {$requestId}, traceId: {$traceId}");
            
            // 如果是鉴权失败，禁用该token
            if ($token && ($errorCode === 'AUTH_FAIL' || $errorMessage === '鉴权失败')) {
                $this->disableToken($token);
                Log::warning("Token已被禁用: {$token}");
            }
            
            return false;
        }
        
        // API请求成功，增加token使用次数
        if ($token) {
            $this->incrementTokenUsage($token);
        }
        
        return $resultData;
    }
    
    /**
     * 检查字符串是否包含任意一个关键词
     * 
     * @param string $haystack 要检查的字符串
     * @param array $needles 关键词数组
     * @return bool 是否包含任意一个关键词
     */
    protected function containsAny($haystack, $needles)
    {
        foreach ($needles as $needle) {
            if (strpos($haystack, $needle) !== false) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 禁用失效的Token
     * 
     * @param string $token 需要禁用的token
     * @return bool 是否成功禁用
     */
    protected function disableToken($token)
    {
        try {
            // 查找对应的token记录
            $tokenModel = Token::where('token', $token)->find();
            
            if ($tokenModel) {
                // 将token状态设置为0（禁用）
                $tokenModel->status = 0;
                $tokenModel->save();
                return true;
            }
            
            return false;
        } catch (\Throwable $e) {
            Log::error('禁用Token失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 增加Token的使用次数
     * 
     * @param string $token 需要增加使用次数的token
     * @return bool 是否成功增加
     */
    protected function incrementTokenUsage($token)
    {
        try {
            // 直接使用inc方法增加使用次数，无需先查询
            $result = Token::where('token', $token)->inc('usage_count')->update();
            return $result > 0;
        } catch (\Throwable $e) {
            Log::error('增加Token使用次数失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 通用的数据同步处理方法
     * 
     * @param callable $dataFetchCallback 获取数据的回调函数
     * @param callable $processCallback 处理数据的回调函数
     * @param string $errorMessage 错误消息
     * @param string $modelClass 用于事务处理的模型类名
     * @return bool 是否成功
     */
    protected static function handleSync($dataFetchCallback, $processCallback, $errorMessage, $modelClass)
    {
        $samClub = new SamClub();
        $device_id = '';
        $ip = get_sys_config('sam_proxy_ip');

        try {
            // 获取有效的token在回调函数内部进行
            $instance = new self();
            
            // 获取数据
            $result = $dataFetchCallback($samClub, $instance, $device_id, $ip);

            
            // 如果是false，表示获取token失败或接口调用失败，在回调中已处理日志
            if ($result === false) {
                return false;
            }
            
            // 解析结果
            $resultData = $result;
            
            if (!$resultData || !isset($resultData['data'])) {
                return false;
            }
            
            // 处理数据（移除显式事务，使用框架自动事务）
            $processCallback($resultData['data'], $instance, $device_id, $ip, $samClub);
            
            return true;
            
        } catch (\Throwable $e) {
            Log::error($errorMessage . ': ' . $e->getMessage());

            return true; // 返回true继续尝试下一次任务
        }
    }
    
    /**
     * 通用的数据获取和保存方法
     * 
     * @param string $taskName 任务名称
     * @param string $controllerName 控制器名称
     * @param string $methodName 方法名称
     * @param string $cron cron表达式
     * @param string $taskRemark 任务备注
     * @param callable $dataFetchCallback 获取数据的回调函数
     * @param callable $dataProcessCallback 处理数据的回调函数
     * @param string $successMessage 成功消息
     * @param string $errorMessage 错误消息
     * @param string $modelClass 用于事务处理的模型类名
     * @return Response 响应对象
     */
    protected function handleDataFetch(
        $taskName,
        $controllerName,
        $methodName, 
        $cron,
        $taskRemark,
        $dataFetchCallback,
        $dataProcessCallback,
        $successMessage,
        $errorMessage,
        $modelClass
    ) {
        $request = request();
        $create_task = $request->param('create_task', 0);
        
        // 如果需要创建定时任务，先获取token进行验证
        if ($create_task) {
            $tokenInfo = $this->getValidToken();
            if (!$tokenInfo) {
                return $this->error('没有找到有效的token');
            }
            
            $taskCreated = self::createTask($taskName, $controllerName, $methodName, $cron, $taskRemark);
            
            if ($taskCreated) {
                return $this->success('创建定时任务成功', [
                    'count' => $modelClass::count(),
                    'token_phone' => $tokenInfo['phone'],
                    'task_created' => true
                ]);
            }
        }
        
        $samClub = new SamClub();
        
        // 获取数据 - 在回调函数内部获取token
        $result = $dataFetchCallback($samClub, $this);
        
        // 如果是false，表示获取token失败或接口调用失败
        if ($result === false) {
            return $this->error($errorMessage);
        }
        
        $resultData = $result;
        
        if (!$resultData || !isset($resultData['data'])) {
            return $this->error($errorMessage, $resultData ?? []);
        }
        
        $processedCount = 0;
        
        try {
            // 处理数据并保存（移除显式事务，使用框架自动事务）
            $processedCount = $dataProcessCallback($resultData['data'], $this);

        } catch (\Throwable $e) {
            Log::error($errorMessage . ': ' . $e->getMessage());
            return $this->error($errorMessage . ': ' . $e->getMessage());
        }
        
        // 在try-catch块外返回成功
        return $this->success($successMessage, [
            'count' => $processedCount
        ]);
    }
    
    /**
     * 批量保存数据的通用方法
     * 
     * @param array $items 要保存的数据项数组
     * @param string $idField 唯一标识字段名
     * @param callable $buildDataCallback 构建数据的回调函数
     * @param string $modelClass 模型类名
     * @return int 成功保存的数量
     */
    protected static function batchSaveData($items, $idField, $buildDataCallback, $modelClass)
    {
        if (empty($items)) {
            return 0;
        }
        
        // 提取所有ID
        $ids = array_column($items, $idField);
        
        // 一次性查询所有已存在的记录
        $existsMap = [];
        if (!empty($ids)) {
            $existsRecords = $modelClass::where($idField, 'in', $ids)->select();
            foreach ($existsRecords as $record) {
                $existsMap[$record[$idField]] = $record;
            }
        }
        
        $successCount = 0;
        $updateData = [];
        $insertData = [];
        $now = time();
        
        // 批量处理数据
        foreach ($items as $item) {
            if (empty($item[$idField])) {
                continue;
            }
            
            // 获取数据
            $itemData = $buildDataCallback($item, $now);
            
            if (isset($existsMap[$item[$idField]])) {
                // 更新记录
                $updateData[] = array_merge(['id' => $existsMap[$item[$idField]]['id']], $itemData);
            } else {
                // 新增记录
                $itemData['create_time'] = $now;
                $insertData[] = $itemData;
            }
            
            $successCount++;
        }
        
        // 批量更新 - 使用直接更新避免事务冲突
        if (!empty($updateData)) {
            foreach ($updateData as $data) {
                $modelClass::where('id', $data['id'])->update($data);
            }
        }

        // 批量插入 - 使用insertAll避免事务冲突
        if (!empty($insertData)) {
            $modelClass::insertAll($insertData);
        }
        
        return $successCount;
    }
    
    /**
     * 停止所有相关的山姆数据采集任务
     * 
     * @return bool 是否成功停止任务
     */
    protected function stopAllRelatedTasks()
    {
        try {
            // 使用模块植入的Task类
            $taskPrefix = '山姆';
            
            // 查询所有包含"山姆"前缀的正在运行的任务
            $tasks = \app\admin\model\Plantask::where('task_name', 'like', $taskPrefix . '%')
                ->where('status', 1) // 运行中的任务
                ->select();
            
            if ($tasks->isEmpty()) {
                Log::info("没有找到需要停止的山姆相关任务");
                return true;
            }
            
            $stopCount = 0;
            foreach ($tasks as $task) {
                // 将任务状态设为已停止
                $task->status = 3; // 假设0为停止状态
                $task->save();
                
                Log::info("已停止任务: {$task->task_name}, ID: {$task->id}");
                $stopCount++;
            }
            // 微信通知
            wxPush("由于检测到反爬机制，已停止 {$stopCount} 个山姆相关任务");
            Log::alert("由于检测到反爬机制，已停止 {$stopCount} 个山姆相关任务");
            return true;
        } catch (\Throwable $e) {
            // 微信通知
            wxPush("停止山姆相关任务失败: " . $e->getMessage());
            Log::error("停止山姆相关任务失败: " . $e->getMessage());
            return false;
        }
    }
} 