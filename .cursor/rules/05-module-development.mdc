---
description: 模块开发指南
globs: 
alwaysApply: false
---
# 模块开发指南

BuildAdmin支持模块化开发，可以通过模块市场一键安装扩展功能。

## 模块结构

模块存放在 [modules](mdc:modules) 目录下，每个模块是一个独立的目录。

```
modules/
├── modulename/            // 模块目录
│   ├── config/            // 模块配置
│   ├── controller/        // 控制器
│   ├── model/             // 模型
│   ├── service/           // 服务类
│   ├── view/              // 视图
│   ├── public/            // 静态资源
│   ├── webfront/          // 前端代码
│   │   ├── src/
│   │   └── package.json
│   ├── info.ini           // 模块信息
│   ├── install.sql        // 安装SQL
│   ├── uninstall.sql      // 卸载SQL
│   └── menu.php           // 菜单配置
```

## 模块开发规范

### 模块命名
- 模块目录名使用小写字母
- 类名遵循 PSR-4 自动加载规范
- 命名空间为 `modules\modulename`

### 模块信息配置

模块的基本信息在 `info.ini` 文件中定义：

```ini
name = 模块名称
title = 模块标题
description = 模块描述
author = 作者名称
website = 作者网站
version = 1.0.0
state = 1
```

### 模块安装与卸载

- 安装SQL文件：`install.sql`
- 卸载SQL文件：`uninstall.sql`
- 安装逻辑可在模块的服务类中实现

### 前端开发

模块的前端代码放在 `webfront` 目录下，结构与主项目前端相似。

- 菜单配置在 `menu.php` 中定义
- 前端路由自动注册
- 前端组件异步加载

## 模块加载流程

1. 系统启动时自动扫描 modules 目录
2. 加载已安装模块的路由和配置
3. 注册模块的服务和事件
4. 加载模块的前端资源

## 模块通信

- 模块间通过事件进行通信
- 核心功能通过依赖注入方式调用
- 模块可以扩展系统功能
