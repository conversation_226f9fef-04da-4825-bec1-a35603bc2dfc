---
description: 项目目录结构
globs: 
alwaysApply: false
---
# 项目目录结构

BuildAdmin项目采用前后端分离的架构设计，目录结构清晰。

## 后端目录结构

```
├── app                     // 应用目录
│   ├── admin               // 后台管理应用
│   ├── api                 // API应用
│   ├── common              // 公共模块
│   ├── BaseController.php  // 控制器基类
│   └── ...                 // 其他应用文件
├── config                  // 配置目录
├── extend                  // 扩展目录
├── modules                 // 模块目录
├── public                  // WEB部署目录
├── runtime                 // 运行时目录
├── vendor                  // Composer依赖
├── composer.json           // Composer配置
└── think                   // 命令行入口
```

## 前端目录结构

```
├── web                     // 前端项目目录
│   ├── public              // 静态资源
│   ├── src                 // 源代码
│   │   ├── api             // API接口
│   │   ├── components      // 组件
│   │   ├── layout          // 布局
│   │   ├── router          // 路由
│   │   ├── stores          // Pinia状态管理
│   │   ├── styles          // 样式
│   │   ├── utils           // 工具类
│   │   ├── views           // 页面
│   │   ├── App.vue         // 入口组件
│   │   └── main.ts         // 入口文件
│   ├── index.html          // HTML模板
│   ├── package.json        // NPM配置
│   ├── tsconfig.json       // TypeScript配置
│   └── vite.config.ts      // Vite配置
```

## 重要目录说明

### 后端

- [app/admin](mdc:app/admin) - 后台管理应用，包含后台管理相关控制器、模型等
- [app/api](mdc:app/api) - API应用，提供前端接口
- [app/common](mdc:app/common) - 公共模块，包含公共函数、中间件等
- [config](mdc:config) - 项目配置文件
- [modules](mdc:modules) - 扩展模块，可安装的功能模块

### 前端

- [web/src/api](mdc:web/src/api) - API请求接口定义
- [web/src/components](mdc:web/src/components) - 公共组件
- [web/src/router](mdc:web/src/router) - 路由配置
- [web/src/stores](mdc:web/src/stores) - Pinia状态管理
- [web/src/views](mdc:web/src/views) - 页面组件

